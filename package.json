{"name": "package.json", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.74.4", "autoprefixer": "^10.4.17", "axios": "^1.8.4", "postcss": "^8.4.35", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.1", "tailwindcss": "^3.4.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tanstack/react-query-devtools": "^5.74.4", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}